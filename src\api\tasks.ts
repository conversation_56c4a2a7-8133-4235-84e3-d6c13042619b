import request from './index'

// 定义任务类型接口，与store保持一致
export interface Task {
  id: string
  taskid?: string // 任务ID（保留原始字段以便页面使用）
  gj_id: number
  title: string // 使用 gjname 作为标题
  starttime: number // 开始时间戳
  endtime: number // 结束时间戳
  status: string // 状态
  // 保留一些原有字段以兼容现有逻辑
  reward?: number
  description?: string
  steps?: string[]
  progress?: number
  deadline?: string
  // 新增字段
  difficulty?: string // 难度等级
  category?: string // 任务分类
  mediaPlatform?: string // 媒体平台
  publishTime?: string // 发布时间
  userCount?: number // 参与人数
  maxParticipants?: number // 最大参与人数
  // API原始字段
  rwtype_score?: string // 任务积分（保留原始字段）
  rwtype_name?: string // 任务类型名称
  media_name?: string // 媒体平台名称
}

// 定义API请求参数接口
export interface GetTasksParams {
  media_id?: string // 媒体平台ID，非必填
  rwtype_id?: string // 任务类型ID，非必填
  page?: number // 页码，默认为1
  pageSize?: number // 每页数量，默认为10
}

// 定义我的任务列表请求参数接口
export interface GetMyTasksParams {
  rwstatus?: number // 任务状态（0默认 1未完成 2已完成）
  spstatus?: number // 任务审批状态（0默认 1未通过 2已通过）
  page?: number // 页数，默认为1
  limit?: number // 每页条数，默认为10
}

// 定义API响应的任务数据结构（根据实际API响应调整）
export interface ApiTaskItem {
  id: number // 主键id
  taskid?: number // 任务ID（我的任务列表可能使用此字段）
  gj_id: number // 稿件id
  gjname: string // 稿件标题
  starttime: number // 开始时间戳
  endtime: number // 结束时间戳
  status: string // 状态
}

// 定义API响应结构 - 新格式
export interface TaskListResponse {
  code: number
  msg: string
  time: string
  data: {
    total: number // 总数量
    list: ApiTaskItem[] // 任务列表
  }
}

// 定义分页响应接口
export interface PaginatedTasksResponse {
  tasks: Task[]
  total: number
  hasMore: boolean
  currentPage: number
}

// 定义任务详情响应接口 - 根据真实API字段更新
export interface TaskDetailResponse {
  code: number
  msg: string
  time: string
  data: {
    id: string // 主键id
    gj_id: string // 稿件id
    gjname: string // 稿件标题
    gjnote: string // 稿件内容
    yxway: string // 营销方式
    yxtype: string // 营销类型
    yxpic: string // 营销封面图
    media_id: string // 媒体平台ID
    media_name: string // 媒体平台名称
    rwtype_id: string // 任务类型id
    rwtype_name: string // 任务类型名称（如：评论任务）
    rwtype_score: string // 任务积分
    kscount: string // 扩散次数
    xzusers: string // 用户串
    starttime: string // 开始时间
    endtime: string // 结束时间
    wxsend: string // 微信通知
    kstotal: string // 扩散总数
    lqtotal: string // 领取总数
    wctotal: string // 完成总数
    createtime: string // 创建时间
    updatetime: string // 更新时间
    status: string // 状态
  }
}

// 时间戳转换为可读格式
function formatTimestamp(timestamp: number | string): string {
  if (!timestamp) return '未设置'
  
  // 处理字符串类型的时间戳
  const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp
  if (isNaN(ts)) return '时间格式错误'
  
  const date = new Date(ts * 1000)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 将API数据转换为标准Task格式
function transformApiTaskToTask(apiTask: ApiTaskItem): Task {
  // 优先使用taskid字段，如果没有则使用id字段
  return {
    id: String(apiTask.id),
    taskid: apiTask.taskid ? String(apiTask.taskid) : undefined, // 保留原始taskid字段
    gj_id: apiTask.gj_id,
    title: apiTask.gjname || '任务标题',
    starttime: apiTask.starttime,
    endtime: apiTask.endtime,
    status: apiTask.status,
    // 兼容字段
    description: `稿件ID: ${apiTask.gj_id}`,
    reward: 0, // 默认奖励，如果后续API提供可更新
    steps: ['查看任务详情', '完成任务要求', '提交任务结果'],
    progress: apiTask.status === 'completed' ? 100 : 0,
    deadline: formatTimestamp(apiTask.endtime)
  }
}

// 将API详情数据转换为标准Task格式 - 根据新API字段更新
function transformApiTaskDetailToTask(apiTask: any): Task {
  // 计算任务奖励（基于积分转换，如果没有积分则使用默认值）
  const score = parseInt(apiTask.rwtype_score) || 0
  const reward = score > 0 ? score * 0.1 : Math.floor(Math.random() * 50) + 10
  
  // 计算进度（基于完成数和领取数）
  const completed = parseInt(apiTask.wctotal) || 0
  const claimed = parseInt(apiTask.lqtotal) || 0
  const total = parseInt(apiTask.kstotal) || 100
  const progress = total > 0 ? Math.round((completed / total) * 100) : 0
  
  return {
    id: String(apiTask.id),
    gj_id: parseInt(apiTask.gj_id) || 0,
    title: apiTask.gjname || '任务标题',
    starttime: parseInt(apiTask.starttime) || 0,
    endtime: parseInt(apiTask.endtime) || 0,
    status: apiTask.status || 'pending',
    // 详细信息
    description: apiTask.gjnote || `营销方式: ${apiTask.yxway || '未知'}，营销类型: ${apiTask.yxtype || '未知'}`,
    reward: reward,
    steps: [
      '仔细阅读任务要求和稿件内容',
      `完成${apiTask.rwtype_name || '任务'}相关操作`,
      '按照营销方式进行推广',
      '确保内容质量符合标准',
      '提交任务完成结果'
    ],
    progress: progress,
    deadline: formatTimestamp(apiTask.endtime),
    // 额外信息
    difficulty: score > 50 ? '困难' : score > 20 ? '中等' : '简单',
    category: apiTask.rwtype_name || '内容创作',
    mediaPlatform: apiTask.media_name || '小红书',
    publishTime: formatTimestamp(apiTask.createtime || apiTask.starttime),
    userCount: claimed,
    maxParticipants: total,
    // 保留API原始字段
    rwtype_score: apiTask.rwtype_score || '0',
    rwtype_name: apiTask.rwtype_name || '',
    media_name: apiTask.media_name || ''
  }
}

// 获取任务列表 - 支持分页
export function getTasks(params?: GetTasksParams): Promise<PaginatedTasksResponse> {
  // 创建FormData参数
  const formData = new FormData()
  
  // 添加参数到FormData，只有值存在时才添加
  if (params?.media_id) {
    formData.append('media_id', params.media_id)
  }
  if (params?.rwtype_id) {
    formData.append('rwtype_id', params.rwtype_id)
  }
  formData.append('page', String(params?.page || 1))
  formData.append('limit', String(params?.pageSize || 10))

  return request.post('/renwu/getrenwulist', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
    .then((response: any) => {
      console.log('任务列表API响应:', response)
      
      // 检查响应数据结构
      if (response && response.code === 1 && response.data) {
        const { total, list } = response.data
        
        // 将API数据转换为标准Task格式
        const tasks = Array.isArray(list) ? list.map(transformApiTaskToTask) : []
        
        // 计算是否还有更多数据
        const currentPage = params?.page || 1
        const pageSize = params?.pageSize || 10
        const hasMore = (currentPage * pageSize) < total
        
        return {
          tasks,
          total,
          hasMore,
          currentPage
        }
      }
      
      // 如果响应格式不正确，返回空结果
      return {
        tasks: [],
        total: 0,
        hasMore: false,
        currentPage: 1
      }
    })
    .catch(error => {
      console.error('获取任务列表失败:', error)
      
      // 如果是未登录错误，直接返回空结果，因为页面会跳转
      if (error && error.message === "未登录") {
        return {
          tasks: [],
          total: 0,
          hasMore: false,
          currentPage: 1
        }
      }
      
      // API调用失败时，抛出错误
      throw error
    })
}

// 获取任务详情 - 使用新的API接口
export function getTaskDetail(id: string): Promise<Task> {
  // 创建FormData参数
  const formData = new FormData()
  formData.append('renwuid', id)
  
  return request.post('/renwu/getrenwuinfo', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }).then((response: any) => {
    console.log('任务详情API响应:', response)
    
    // 根据新的响应格式处理
    if (response && response.code === 1 && response.data) {
      const taskData = response.data
      return transformApiTaskDetailToTask(taskData)
    } else if (response && response.code === 0) {
      // 处理错误情况
      throw new Error(response.msg || '获取任务详情失败')
    }
    
    throw new Error('任务不存在或数据格式错误')
  }).catch(error => {
    console.error('获取任务详情失败:', error)
    
    // 如果是未登录错误，直接抛出
    if (error && error.message === "未登录") {
      throw error
    }
    
    // 其他错误也抛出
    throw error
  })
}

// 完成任务
export function completeTask(id: string) {
  return request.post(`/renwu/complete/${id}`)
}

// 领取任务奖励
export function claimReward(id: string) {
  return request.post(`/renwu/claim/${id}`)
}

// 领取任务接口响应类型
export interface ClaimTaskResponse {
  code: number
  msg: string
  time: string
  data: any
}

/**
 * 领取任务
 * @param renwuid 任务ID
 * @returns Promise<ClaimTaskResponse>
 */
export function claimTask(renwuid: string): Promise<ClaimTaskResponse> {
  // 创建FormData
  const formData = new FormData()
  formData.append('renwuid', renwuid)

  return request.post('/renwu/getrenwulq', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }).then((response: any) => {
    // API返回的完整响应
    return response
  }).catch((error: any) => {
    // 重要：拦截器已经显示了toast，这里不需要再显示
    // 直接重新抛出错误，让调用方知道失败了
    console.log('claimTask API 错误:', error.message)
    throw error
  })
}

// 提交任务参数类型
export interface SubmitTaskParams {
  renwuid: string // 任务ID
  stashensu: number // 是否申诉（0普通提交 1提交申诉）
  sublink: string // 提交链接
  subpic: File // 提交截图（必填）
  subtext?: string // 其他证明（可选）
}

// 提交任务响应类型
export interface SubmitTaskResponse {
  code: number
  msg: string
  time: string
  data: any
}

/**
 * 提交任务
 * @param params 提交参数
 * @returns Promise<SubmitTaskResponse>
 */
export function submitTask(params: SubmitTaskParams): Promise<SubmitTaskResponse> {
  // 创建FormData
  const formData = new FormData()
  formData.append('renwuid', params.renwuid)
  formData.append('stashensu', String(params.stashensu))
  formData.append('sublink', params.sublink)
  formData.append('subpic', params.subpic) // 必填参数

  // 可选参数
  if (params.subtext) {
    formData.append('subtext', params.subtext)
  }

  return request.post('/renwu/subrenwudata', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }).then((response: any) => {
    // API返回的完整响应
    return response
  }).catch((error: any) => {
    // 重新抛出错误，让调用方处理
    console.log('submitTask API 错误:', error.message)
    throw error
  })
}

/**
 * 获取我的任务列表
 * @param params 请求参数
 * @returns Promise<PaginatedTasksResponse>
 */
export function getMyTasks(params?: GetMyTasksParams): Promise<PaginatedTasksResponse> {
  // 创建FormData参数
  const formData = new FormData()

  // 添加参数到FormData
  if (params?.rwstatus !== undefined) {
    formData.append('rwstatus', String(params.rwstatus))
  }
  if (params?.spstatus !== undefined) {
    formData.append('spstatus', String(params.spstatus))
  }
  formData.append('page', String(params?.page || 1))
  formData.append('limit', String(params?.limit || 10))

  return request.post('/renwu/getrenwulqlist', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
    .then((response: any) => {
      console.log('我的任务列表API响应:', response)

      // 检查响应数据结构
      if (response && response.code === 1 && response.data) {
        const { total, list } = response.data

        // 将API数据转换为标准Task格式
        const tasks = Array.isArray(list) ? list.map(transformApiTaskToTask) : []

        // 计算是否还有更多数据
        const currentPage = params?.page || 1
        const pageSize = params?.limit || 10
        const hasMore = (currentPage * pageSize) < total

        return {
          tasks,
          total,
          hasMore,
          currentPage
        }
      }

      // 如果响应格式不正确，返回空结果
      return {
        tasks: [],
        total: 0,
        hasMore: false,
        currentPage: 1
      }
    })
    .catch(error => {
      console.error('获取我的任务列表失败:', error)

      // 如果是未登录错误，直接返回空结果，因为页面会跳转
      if (error && error.message === "未登录") {
        return {
          tasks: [],
          total: 0,
          hasMore: false,
          currentPage: 1
        }
      }

      // API调用失败时，抛出错误
      throw error
    })
}