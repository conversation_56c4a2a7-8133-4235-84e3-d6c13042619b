// 防重复显示的时间间隔（毫秒）
const DUPLICATE_INTERVAL = 500

// 记录上次显示 toast 的时间
let lastToastTime = 0

/**
 * 创建自定义 Toast 元素
 */
const createCustomToast = (message: string) => {
  // 移除所有已存在的 toast（包括可能的多个实例）
  const existingToasts = document.querySelectorAll('.custom-toast')
  existingToasts.forEach(toast => toast.remove())

  // 创建 toast 元素
  const toast = document.createElement('div')
  toast.className = 'custom-toast'
  toast.textContent = message

  // 设置样式
  Object.assign(toast.style, {
    position: 'fixed',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    color: 'white',
    padding: '12px 20px',
    borderRadius: '8px',
    fontSize: '14px',
    zIndex: '9999',
    maxWidth: '80%',
    textAlign: 'center',
    wordBreak: 'break-word',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
    animation: 'fadeIn 0.3s ease-in-out'
  })

  // 添加动画样式
  if (!document.querySelector('#custom-toast-style')) {
    const style = document.createElement('style')
    style.id = 'custom-toast-style'
    style.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
        to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
      }
      @keyframes fadeOut {
        from { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        to { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
      }
    `
    document.head.appendChild(style)
  }

  // 添加到页面
  document.body.appendChild(toast)

  // 3秒后自动移除
  setTimeout(() => {
    if (toast.parentNode) {
      toast.style.animation = 'fadeOut 0.3s ease-in-out'
      setTimeout(() => {
        if (toast.parentNode) {
          toast.remove()
        }
      }, 300)
    }
  }, 3000)
}

/**
 * 安全的 showToast 包装函数
 * 防止空消息和重复调用
 * @param message 要显示的消息
 */
export const showToast = (message: any) => {
  // 防止短时间内重复显示 toast
  const now = Date.now()
  if (now - lastToastTime < DUPLICATE_INTERVAL) {
    if (process.env.NODE_ENV === 'development') {
      console.log('防止重复 toast，忽略此次调用:', message)
    }
    return
  }
  lastToastTime = now

  // 确保消息不为空
  let safeMessage = message

  if (!message || typeof message !== 'string' || message.trim() === '') {
    safeMessage = '操作失败，请稍后重试'
    if (process.env.NODE_ENV === 'development') {
      console.warn('showToast 收到空消息，使用默认消息:', { originalMessage: message, safeMessage })
    }
  }

  if (process.env.NODE_ENV === 'development') {
    console.log('showToast 调用:', { originalMessage: message, finalMessage: safeMessage })
  }

  // 只使用自定义 Toast，完全避免 Vant Toast 的问题
  createCustomToast(safeMessage)
}

export default showToast
