import request from './index'

// 媒体平台接口响应类型
export interface MediaPlatform {
  id: number
  name: string
}

// 部门数据接口类型定义
export interface Department {
  id: number
  name: string
  childarr: Department[]
}

// 通用API响应类型
export interface CommonApiResponse<T> {
  code: number
  msg: string
  time: string
  data: T
}

/**
 * 获取媒体平台列表
 * @returns Promise<MediaPlatform[]>
 */
export const getMediaList = async (): Promise<MediaPlatform[]> => {
  const response = await request.get('/common/getmedialist')
  return response.data
}

/**
 * 获取部门列表
 * @returns Promise<Department[]>
 */
export const getDepartmentList = async (): Promise<Department[]> => {
  const response = await request.get('/common/getbumen')
  return response.data
}