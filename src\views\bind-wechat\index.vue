<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { showLoadingToast, closeToast } from 'vant'
import { showToast } from '@/utils/toast'
import { useUserStore } from '@/stores/user'
import { bindWechat, getWechatQrCode, getWechatConfig } from '@/api/user'

// 路由实例
const router = useRouter()

// 用户状态
const userStore = useUserStore()

// 页面状态
const qrCodeUrl = ref('')
const isLoading = ref(false)
const bindingStatus = ref<'waiting' | 'success' | 'failed'>('waiting')
const pollingTimer = ref<number | null>(null)
const wechatConfig = ref<any>(null)
const bindingMethod = ref<'qrcode' | 'oauth'>('qrcode')

// 获取微信配置信息
const getWechatConfigInfo = async () => {
  try {
    const response = await getWechatConfig()

    if (response.status === '1') {
      wechatConfig.value = response
      return response
    } else {
      showToast(response.msg || '获取微信配置失败')
      return null
    }
  } catch (error) {
    console.error('获取微信配置失败:', error)
    showToast('获取微信配置失败，请稍后重试')
    return null
  }
}

// 检查是否在微信环境中
const isWechatBrowser = () => {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('micromessenger')
}

// 微信授权绑定
const wechatOAuthBind = async () => {
  const config = await getWechatConfigInfo()
  if (!config) return

  // 构建微信授权URL
  const redirectUri = encodeURIComponent(window.location.href)
  const state = 'bind_wechat_' + Date.now()
  const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${config.appid}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_userinfo&state=${state}#wechat_redirect`

  // 跳转到微信授权页面
  window.location.href = authUrl
}

// 获取微信绑定二维码
const getQrCode = async () => {
  isLoading.value = true

  try {
    const response = await getWechatQrCode()

    if (response.code === 1) {
      qrCodeUrl.value = response.data.qrcode || response.data.url
      // 开始轮询检查绑定状态
      startPolling()
    } else {
      showToast(response.msg || '获取二维码失败')
    }
  } catch (error) {
    console.error('获取微信二维码失败:', error)
    showToast('获取二维码失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

// 开始轮询检查绑定状态
const startPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
  }
  
  pollingTimer.value = window.setInterval(async () => {
    try {
      // 重新获取用户信息检查绑定状态
      const memberData = await userStore.fetchMemberInfo()
      
      if (memberData && memberData.isbdwx === 1) {
        // 绑定成功
        bindingStatus.value = 'success'
        stopPolling()
        
        showToast('微信绑定成功！')
        
        // 延迟跳转回个人中心
        setTimeout(() => {
          router.replace('/user')
        }, 1500)
      }
    } catch (error) {
      console.error('检查绑定状态失败:', error)
    }
  }, 3000) // 每3秒检查一次
}

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
    pollingTimer.value = null
  }
}

// 刷新二维码
const refreshQrCode = () => {
  bindingStatus.value = 'waiting'
  getQrCode()
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 初始化绑定流程
const initBindingFlow = async () => {
  // 检查URL中是否有微信授权返回的code
  const urlParams = new URLSearchParams(window.location.search)
  const code = urlParams.get('code')
  const state = urlParams.get('state')

  if (code && state && state.startsWith('bind_wechat_')) {
    // 处理微信授权返回
    await handleWechatAuthCallback(code)
    return
  }

  // 根据环境选择绑定方式
  if (isWechatBrowser()) {
    bindingMethod.value = 'oauth'
    // 在微信环境中，直接启动授权流程
    await wechatOAuthBind()
  } else {
    bindingMethod.value = 'qrcode'
    // 在非微信环境中，显示二维码
    await getQrCode()
  }
}

// 处理微信授权回调
const handleWechatAuthCallback = async (code: string) => {
  isLoading.value = true

  try {
    const response = await bindWechat({ code })

    if (response.code === 1) {
      bindingStatus.value = 'success'
      showToast('微信绑定成功！')

      // 重新加载用户信息
      await userStore.fetchMemberInfo()

      // 延迟跳转回个人中心
      setTimeout(() => {
        router.replace('/user')
      }, 1500)
    } else {
      bindingStatus.value = 'failed'
      showToast(response.msg || '绑定失败')
    }
  } catch (error) {
    console.error('微信绑定失败:', error)
    bindingStatus.value = 'failed'
    showToast('绑定失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

// 页面加载时初始化
onMounted(() => {
  initBindingFlow()
})

// 页面卸载时清理定时器
onUnmounted(() => {
  stopPolling()
})
</script>

<template>
  <div class="bind-wechat-container">
    <!-- 导航栏 -->
    <van-nav-bar 
      title="绑定微信" 
      left-arrow
      fixed 
      placeholder
      background="#ff6b35"
      color="#fff"
      @click-left="goBack"
    />
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 说明文字 -->
      <div class="description">
        <h3>绑定微信账号</h3>
        <p v-if="bindingMethod === 'qrcode'">
          请使用微信扫描下方二维码完成绑定，绑定后您可以使用微信快速登录。
        </p>
        <p v-else-if="bindingMethod === 'oauth'">
          正在跳转到微信授权页面，请稍候...
        </p>
        <p v-else>
          绑定微信后，您可以使用微信快速登录，享受更便捷的服务体验。
        </p>
      </div>
      
      <!-- 二维码区域 -->
      <div class="qrcode-section">
        <div v-if="isLoading" class="loading-container">
          <van-loading size="24" color="#ff6b35" />
          <p class="loading-text">正在生成二维码...</p>
        </div>
        
        <div v-else-if="bindingStatus === 'success'" class="success-container">
          <van-icon name="checked" size="48" color="#07c160" />
          <p class="success-text">绑定成功！</p>
          <p class="success-desc">正在跳转...</p>
        </div>

        <div v-else-if="bindingStatus === 'failed'" class="failed-container">
          <van-icon name="cross" size="48" color="#ee0a24" />
          <p class="failed-text">绑定失败</p>
          <p class="failed-desc">请重试或联系客服</p>
          <van-button
            type="primary"
            round
            @click="initBindingFlow"
            color="#ff6b35"
            style="margin-top: 16px;"
          >
            重新绑定
          </van-button>
        </div>

        <div v-else-if="bindingMethod === 'oauth'" class="oauth-container">
          <van-loading size="24" color="#ff6b35" />
          <p class="oauth-text">正在跳转到微信授权...</p>
          <p class="oauth-desc">如果没有自动跳转，请点击下方按钮</p>
          <van-button
            type="primary"
            round
            @click="wechatOAuthBind"
            color="#ff6b35"
            style="margin-top: 16px;"
          >
            手动授权
          </van-button>
        </div>
        
        <div v-else class="qrcode-container">
          <div class="qrcode-wrapper">
            <img v-if="qrCodeUrl" :src="qrCodeUrl" alt="微信二维码" class="qrcode-image" />
            <div v-else class="qrcode-placeholder">
              <van-icon name="qr" size="48" color="#c8c9cc" />
              <p>二维码加载失败</p>
            </div>
          </div>
          
          <div class="qrcode-tips">
            <p class="tip-title">扫码步骤：</p>
            <ol class="tip-list">
              <li>打开微信扫一扫</li>
              <li>扫描上方二维码</li>
              <li>在微信中确认绑定</li>
            </ol>
          </div>
          
          <van-button 
            type="primary" 
            round 
            block 
            @click="refreshQrCode"
            :loading="isLoading"
            color="#ff6b35"
          >
            刷新二维码
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bind-wechat-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.content {
  padding: 20px 16px;
}

.description {
  text-align: center;
  margin-bottom: 32px;
}

.description h3 {
  font-size: 20px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 12px 0;
}

.description p {
  font-size: 14px;
  color: #969799;
  line-height: 1.5;
  margin: 0;
}

.qrcode-section {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.loading-text {
  margin: 16px 0 0 0;
  font-size: 14px;
  color: #969799;
}

.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.success-text {
  font-size: 18px;
  font-weight: 600;
  color: #07c160;
  margin: 16px 0 8px 0;
}

.success-desc {
  font-size: 14px;
  color: #969799;
  margin: 0;
}

.failed-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.failed-text {
  font-size: 18px;
  font-weight: 600;
  color: #ee0a24;
  margin: 16px 0 8px 0;
}

.failed-desc {
  font-size: 14px;
  color: #969799;
  margin: 0;
}

.oauth-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.oauth-text {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin: 16px 0 8px 0;
}

.oauth-desc {
  font-size: 14px;
  color: #969799;
  margin: 0;
  text-align: center;
}

.qrcode-container {
  text-align: center;
}

.qrcode-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.qrcode-image {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  border: 1px solid #ebedf0;
}

.qrcode-placeholder {
  width: 200px;
  height: 200px;
  border: 1px solid #ebedf0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.qrcode-placeholder p {
  margin: 8px 0 0 0;
  font-size: 12px;
  color: #c8c9cc;
}

.qrcode-tips {
  margin-bottom: 24px;
  text-align: left;
}

.tip-title {
  font-size: 14px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 12px 0;
}

.tip-list {
  margin: 0;
  padding-left: 20px;
}

.tip-list li {
  font-size: 13px;
  color: #646566;
  line-height: 1.6;
  margin-bottom: 4px;
}

/* 自定义导航栏样式 */
:deep(.van-nav-bar) {
  background: linear-gradient(90deg, #ff6b35, #ff8c69);
}

:deep(.van-nav-bar__title) {
  color: #fff;
  font-weight: 600;
}

:deep(.van-nav-bar__left .van-icon) {
  color: #fff;
}
</style>
