{"name": "xia<PERSON><PERSON><PERSON>-task-h5", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.0", "pinia": "^2.1.7", "vant": "^4.8.0", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.8.10", "@vitejs/plugin-vue": "^4.5.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "sass": "^1.69.5", "typescript": "^5.1.6", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.0", "vue-tsc": "^1.8.25"}}