<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from '@/utils/toast'
import { register, sendCode } from '@/api/user'
import type { RegisterParams, SendCodeParams } from '@/api/user'
import { getDepartmentList } from '@/api/common'
import type { Department } from '@/api/common'

// 路由实例
const router = useRouter()

// 表单数据
const registerForm = reactive({
  phone: '',
  code: '',
  password: '',
  confirmPassword: '',
  username: '', // 新增用户工号
  nickname: '', // 新增用户姓名
  departid: 0   // 新增用户部门ID
})

// 部门相关数据
const departmentList = ref<Department[]>([])
const showDepartmentPicker = ref(false)
const departmentColumns = ref<any[]>([])
const selectedDepartmentText = ref('')
const currentSelectedPath = ref<any[]>([])

// 加载状态
const loading = ref(false)
// 验证码发送状态
const sendingCode = ref(false)
// 倒计时状态
const countdown = ref(0)
// 倒计时定时器
let countdownTimer: NodeJS.Timeout | null = null

// 获取部门数据
const fetchDepartmentList = async () => {
  try {
    const data = await getDepartmentList()
    departmentList.value = data
    // 构建级联选择器的数据结构
    buildDepartmentColumns()
  } catch (error) {
    console.error('获取部门列表失败:', error)
  }
}

// 构建部门级联选择器数据
const buildDepartmentColumns = () => {
  const buildOptions = (departments: Department[]): any[] => {
    return departments.map(dept => ({
      text: dept.name,
      value: dept.id,
      children: dept.childarr && dept.childarr.length > 0 ? buildOptions(dept.childarr) : undefined
    }))
  }

  departmentColumns.value = buildOptions(departmentList.value)
}

// 处理部门选择
const onDepartmentConfirm = ({ selectedOptions }: any) => {
  if (selectedOptions && selectedOptions.length > 0) {
    const lastSelected = selectedOptions[selectedOptions.length - 1]
    registerForm.departid = lastSelected.value
    selectedDepartmentText.value = selectedOptions.map((option: any) => option.text).join(' / ')
  }
  showDepartmentPicker.value = false
}

// 处理级联选择器值变化（跟踪当前选择路径）
const onDepartmentChange = ({ selectedOptions }: any) => {
  currentSelectedPath.value = selectedOptions || []
}

// 确认当前选择的部门（允许选择任意级别）
const confirmCurrentSelection = () => {
  if (currentSelectedPath.value.length > 0) {
    const lastSelected = currentSelectedPath.value[currentSelectedPath.value.length - 1]
    registerForm.departid = lastSelected.value
    selectedDepartmentText.value = currentSelectedPath.value.map((option: any) => option.text).join(' / ')
    showDepartmentPicker.value = false
  }
}

// 显示部门选择器
const showDepartmentSelector = () => {
  if (departmentList.value.length === 0) {
    fetchDepartmentList()
  }
  showDepartmentPicker.value = true
}

// 发送验证码
const sendCodeHandler = async () => {
  // 手机号格式验证
  if (!registerForm.phone) {
    showToast('请先输入手机号')
    return
  }
  
  const phoneReg = /^1[3-9]\d{9}$/
  if (!phoneReg.test(registerForm.phone)) {
    showToast('请输入正确的手机号码')
    return
  }
  
  sendingCode.value = true
  try {
    const params: SendCodeParams = {
      phone: registerForm.phone
    }
    
    await sendCode(params)
    showToast('验证码已发送')
    
    // 开始倒计时
    countdown.value = 60
    countdownTimer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        if (countdownTimer) {
          clearInterval(countdownTimer)
          countdownTimer = null
        }
      }
    }, 1000)
  } catch (error: any) {
    console.error('发送验证码失败:', error)
    // 显示错误信息
    const errorMsg = error?.message || error?.response?.msg || '发送验证码失败，请重试'
    showToast(errorMsg)
  } finally {
    sendingCode.value = false
  }
}

// 注册方法
const handleRegister = async () => {
  // 表单验证
  if (!registerForm.phone || !registerForm.code || !registerForm.password ||
      !registerForm.confirmPassword || !registerForm.username || !registerForm.nickname ||
      !registerForm.departid) {
    showToast('请填写完整信息')
    return
  }
  
  // 手机号格式验证
  const phoneReg = /^1[3-9]\d{9}$/
  if (!phoneReg.test(registerForm.phone)) {
    showToast('请输入正确的手机号码')
    return
  }
  
  // 验证码验证
  if (!registerForm.code || registerForm.code.length < 4) {
    showToast('请输入正确的验证码')
    return
  }
  
  // 密码长度验证
  if (registerForm.password.length < 6) {
    showToast('密码长度不能少于6位')
    return
  }
  
  // 密码确认验证
  if (registerForm.password !== registerForm.confirmPassword) {
    showToast('两次输入的密码不一致')
    return
  }
  
  // 开始注册
  loading.value = true
  try {
    const params: RegisterParams = {
      phone: registerForm.phone,
      pwd: registerForm.password,
      repwd: registerForm.confirmPassword,
      yzm: registerForm.code,
      username: registerForm.username, // 添加用户工号
      nickname: registerForm.nickname, // 添加用户姓名
      departid: registerForm.departid  // 添加用户部门ID
    }
    
    const response = await register(params)
    
    if (response.code !== 1) {
      showToast(response.msg || '注册失败，请重试')
      return
    }

    // 注册成功
    showToast('注册成功，正在跳转...')
    
    // 清理定时器
    clearCountdownTimer()
    
    // 延迟跳转，让用户看到成功提示
    setTimeout(() => {
      router.push('/')
    }, 1500)
    
  } catch (error: any) {
    console.error('注册失败:', error)
    // 显示错误信息
    const errorMsg = error?.message || error?.response?.msg || '注册失败，请重试'
    showToast(errorMsg)
  } finally {
    loading.value = false
  }
}

// 跳转到登录页面
const toLogin = () => {
  router.push('/login')
}

// 组件销毁时清理定时器
const clearCountdownTimer = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// 在组件卸载时清理定时器
import { onUnmounted } from 'vue'
onUnmounted(() => {
  clearCountdownTimer()
})

// 组件挂载时获取部门数据
onMounted(() => {
  fetchDepartmentList()
})
</script>

<template>
  <div class="register-container">
    <!-- 简化的头部 -->
    <div class="register-header">
      <h1 class="page-title">注册</h1>
    </div>
    
    <!-- 注册表单 -->
    <div class="register-form">
      <div class="form-card">
        <van-form @submit="handleRegister">
          <div class="form-fields">
            <van-field
              v-model="registerForm.username"
              name="username"
              placeholder="请输入用户工号"
              clearable
              :border="false"
              :rules="[{ required: true, message: '请输入用户工号' }]"
            >
              <template #left-icon>
                <van-icon name="manager-o" />
              </template>
            </van-field>
            
            <van-field
              v-model="registerForm.nickname"
              name="nickname"
              placeholder="请输入用户姓名"
              clearable
              :border="false"
              :rules="[{ required: true, message: '请输入用户姓名' }]"
            >
              <template #left-icon>
                <van-icon name="contact" />
              </template>
            </van-field>

            <van-field
              v-model="selectedDepartmentText"
              name="department"
              placeholder="请选择所属部门"
              readonly
              is-link
              :border="false"
              :rules="[{ required: true, message: '请选择所属部门' }]"
              @click="showDepartmentSelector"
            >
              <template #left-icon>
                <van-icon name="cluster-o" />
              </template>
            </van-field>
            
            <van-field
              v-model="registerForm.phone"
              name="phone"
              placeholder="请输入手机号"
              type="tel"
              maxlength="11"
              clearable
              :border="false"
              :rules="[{ required: true, message: '请输入手机号' }]"
            >
              <template #left-icon>
                <van-icon name="phone-o" />
              </template>
            </van-field>
            
            <div class="code-field">
                          <van-field
              v-model="registerForm.code"
              name="code"
              placeholder="请输入验证码"
              type="number"
              maxlength="4"
              clearable
              :border="false"
              :rules="[{ required: true, message: '请输入验证码' }]"
            >
                <template #left-icon>
                  <van-icon name="shield-o" />
                </template>
              </van-field>
              <van-button
                size="small"
                type="primary"
                :loading="sendingCode"
                :disabled="countdown > 0"
                @click="sendCodeHandler"
                class="code-btn"
              >
                {{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
              </van-button>
            </div>
            
            <van-field
              v-model="registerForm.password"
              type="password"
              name="password"
              placeholder="请输入密码"
              clearable
              :border="false"
              :rules="[{ required: true, message: '请输入密码' }]"
            >
              <template #left-icon>
                <van-icon name="lock" />
              </template>
            </van-field>
            
            <van-field
              v-model="registerForm.confirmPassword"
              type="password"
              name="confirmPassword"
              placeholder="请再次输入密码"
              clearable
              :border="false"
              :rules="[{ required: true, message: '请再次输入密码' }]"
            >
              <template #left-icon>
                <van-icon name="lock" />
              </template>
            </van-field>
          </div>
          
          <div class="form-actions">
            <van-button 
              round 
              block 
              type="primary" 
              native-type="submit" 
              :loading="loading"
              size="large"
              color="#ff6b35"
              class="register-btn"
            >
              注册
            </van-button>
          </div>
          
          <div class="form-footer">
            <div class="login-link" @click="toLogin">
              已有账号？<span class="link-text">立即登录</span>
            </div>
          </div>
        </van-form>
      </div>
    </div>

    <!-- 部门选择器 -->
    <van-popup v-model:show="showDepartmentPicker" position="bottom">
      <div class="department-picker">
        <div class="picker-header">
          <span class="picker-title">选择部门</span>
          <van-button
            type="primary"
            size="small"
            @click="confirmCurrentSelection"
            :disabled="currentSelectedPath.length === 0"
          >
            确认选择
          </van-button>
        </div>
        <van-cascader
          v-model="registerForm.departid"
          :options="departmentColumns"
          @close="showDepartmentPicker = false"
          @finish="onDepartmentConfirm"
          @change="onDepartmentChange"
        />
      </div>
    </van-popup>
  </div>
</template>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #ff6741 0%, #ff8c5c 100%);
  display: flex;
  flex-direction: column;
  padding-bottom: 50px;
}

.register-header {
  padding: 30px 20px 20px;
  text-align: center;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  color: #fff;
  margin: 0;
  letter-spacing: 1px;
}

.register-form {
  flex: 1;
  padding: 0 20px;
}

.form-card {
  background: #fff;
  border-radius: 20px;
  padding: 40px 30px;
  box-shadow: 0 -8px 30px rgba(0, 0, 0, 0.1);
}

.form-fields {
  margin-bottom: 40px;
}

.form-fields .van-field {
  background: #f5f5f5;
  border-radius: 12px;
  margin-bottom: 20px;
  padding: 0 20px;
  height: 56px;
  display: flex;
  align-items: center;
}

.code-field {
  position: relative;
  margin-bottom: 20px;
}

.code-field .van-field {
  margin-bottom: 0;
  padding-right: 120px;
}

.code-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  height: 36px;
  padding: 0 16px;
  font-size: 14px;
  border-radius: 18px;
  background: linear-gradient(90deg, #ff6741, #ff8c5c);
  border: none;
  color: #fff;
  min-width: 100px;
}

.code-btn:disabled {
  background: #f5f5f5 !important;
  color: #969799 !important;
}

.form-actions {
  margin-bottom: 30px;
}

.register-btn {
  height: 50px !important;
  font-size: 18px;
  font-weight: 600;
}

.form-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.login-link {
  font-size: 15px;
  color: #646566;
  cursor: pointer;
}

.link-text {
  color: #ff6741;
  font-weight: 500;
}

/* 自定义字段样式 */
:deep(.van-field__left-icon) {
  color: #969799;
  margin-right: 12px;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.van-field__control) {
  font-size: 16px;
  color: #333;
  line-height: 1.5;
  display: flex;
  align-items: center;
}

:deep(.van-field__control)::placeholder {
  color: #969799;
  font-size: 16px;
  line-height: 1.5;
}

:deep(.van-field) {
  border: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  min-height: 56px;
}

:deep(.van-field__body) {
  display: flex;
  align-items: center;
  width: 100%;
}

:deep(.van-field--focused) {
  background: #fff;
  box-shadow: 0 0 0 2px rgba(255, 103, 65, 0.2);
}

/* 自定义按钮样式 */
:deep(.van-button--primary) {
  background: linear-gradient(90deg, #ff6741, #ff8c5c);
  border: none;
  box-shadow: 0 4px 15px rgba(255, 103, 65, 0.3);
}

:deep(.van-button--primary:active) {
  background: linear-gradient(90deg, #e55a37, #e5804f);
}

/* 部门选择器样式 */
.department-picker {
  background: #fff;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.picker-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.picker-header .van-button {
  height: 32px;
  padding: 0 16px;
  font-size: 14px;
  border-radius: 16px;
}
</style> 

