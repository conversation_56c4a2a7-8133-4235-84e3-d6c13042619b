<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from '@/utils/toast'
import { resetPassword, sendCode } from '@/api/user'
import type { ResetPasswordParams, SendCodeParams } from '@/api/user'

// 路由实例
const router = useRouter()

// 表单数据
const resetForm = reactive({
  phone: '',
  code: '',
  password: '',
  confirmPassword: ''
})

// 加载状态
const loading = ref(false)
// 验证码发送状态
const sendingCode = ref(false)
// 倒计时状态
const countdown = ref(0)
// 倒计时定时器
let countdownTimer: NodeJS.Timeout | null = null

// 发送验证码
const sendCodeHandler = async () => {
  // 手机号格式验证
  if (!resetForm.phone) {
    showToast('请先输入手机号')
    return
  }
  
  const phoneReg = /^1[3-9]\d{9}$/
  if (!phoneReg.test(resetForm.phone)) {
    showToast('请输入正确的手机号码')
    return
  }
  
  sendingCode.value = true
  try {
    const params: SendCodeParams = {
      phone: resetForm.phone
    }
    
    await sendCode(params)
    showToast('验证码已发送')
    
    // 开始倒计时
    countdown.value = 60
    countdownTimer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        if (countdownTimer) {
          clearInterval(countdownTimer)
          countdownTimer = null
        }
      }
    }, 1000)
  } catch (error: any) {
    console.error('发送验证码失败:', error)
    // 显示错误信息
    const errorMsg = error?.message || error?.response?.msg || '发送验证码失败，请重试'
    showToast(errorMsg)
  } finally {
    sendingCode.value = false
  }
}

// 重置密码方法
const handleResetPassword = async () => {
  // 表单验证
  if (!resetForm.phone || !resetForm.code || !resetForm.password || !resetForm.confirmPassword) {
    showToast('请填写完整信息')
    return
  }
  
  // 手机号格式验证
  const phoneReg = /^1[3-9]\d{9}$/
  if (!phoneReg.test(resetForm.phone)) {
    showToast('请输入正确的手机号码')
    return
  }
  
  // 验证码验证
  if (!resetForm.code || resetForm.code.length < 4) {
    showToast('请输入正确的验证码')
    return
  }
  
  // 密码长度验证
  if (resetForm.password.length < 6) {
    showToast('密码长度不能少于6位')
    return
  }
  
  // 密码确认验证
  if (resetForm.password !== resetForm.confirmPassword) {
    showToast('两次输入的密码不一致')
    return
  }
  
  // 开始重置密码
  loading.value = true
  try {
    const params: ResetPasswordParams = {
      phone: resetForm.phone,
      pwd: resetForm.password,
      repwd: resetForm.confirmPassword,
      yzmcode: resetForm.code
    }
    
    const response = await resetPassword(params)
    
    // 重置密码成功
    showToast('密码重置成功，正在跳转...')
    
    // 清理定时器
    clearCountdownTimer()
    
    // 延迟跳转，让用户看到成功提示
    setTimeout(() => {
      router.push('/login')
    }, 1500)
    
  } catch (error: any) {
    console.error('重置密码失败:', error)
    // 显示错误信息
    const errorMsg = error?.message || error?.response?.msg || '重置密码失败，请重试'
    showToast(errorMsg)
  } finally {
    loading.value = false
  }
}

// 返回登录页面
const toLogin = () => {
  router.push('/login')
}

// 组件销毁时清理定时器
const clearCountdownTimer = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// 在组件卸载时清理定时器
import { onUnmounted } from 'vue'
onUnmounted(() => {
  clearCountdownTimer()
})
</script>

<template>
  <div class="forgot-password-container">
    <!-- 头部 -->
    <div class="forgot-password-header">
      <van-nav-bar
        title="找回密码"
        left-arrow
        @click-left="toLogin"
        :border="false"
        :fixed="false"
      />
    </div>
    
    <!-- 找回密码表单 -->
    <div class="forgot-password-form">
      <div class="form-card">
        <div class="form-title">
          <h2>重置密码</h2>
        </div>
        
        <van-form @submit="handleResetPassword">
          <div class="form-fields">
            <van-field
              v-model="resetForm.phone"
              name="phone"
              placeholder="请输入手机号"
              type="tel"
              maxlength="11"
              clearable
              :border="false"
              :rules="[{ required: true, message: '请输入手机号' }]"
            >
              <template #left-icon>
                <van-icon name="phone-o" />
              </template>
            </van-field>
            
            <div class="code-field">
              <van-field
                v-model="resetForm.code"
                name="code"
                placeholder="请输入验证码"
                type="number"
                maxlength="4"
                clearable
                :border="false"
                :rules="[{ required: true, message: '请输入验证码' }]"
              >
                <template #left-icon>
                  <van-icon name="shield-o" />
                </template>
              </van-field>
              <van-button
                size="small"
                type="primary"
                :loading="sendingCode"
                :disabled="countdown > 0"
                @click="sendCodeHandler"
                class="code-btn"
              >
                {{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
              </van-button>
            </div>
            
            <van-field
              v-model="resetForm.password"
              type="password"
              name="password"
              placeholder="请输入新密码"
              clearable
              :border="false"
              :rules="[{ required: true, message: '请输入新密码' }]"
            >
              <template #left-icon>
                <van-icon name="lock" />
              </template>
            </van-field>
            
            <van-field
              v-model="resetForm.confirmPassword"
              type="password"
              name="confirmPassword"
              placeholder="请确认新密码"
              clearable
              :border="false"
              :rules="[{ required: true, message: '请确认新密码' }]"
            >
              <template #left-icon>
                <van-icon name="lock" />
              </template>
            </van-field>
          </div>
          
          <div class="form-actions">
            <van-button 
              round 
              block 
              type="primary" 
              native-type="submit" 
              :loading="loading"
              size="large"
              color="#ff6b35"
              class="reset-btn"
            >
              重置密码
            </van-button>
          </div>
          
          <div class="form-footer">
            <div class="login-link" @click="toLogin">
              返回登录
            </div>
          </div>
        </van-form>
      </div>
    </div>
  </div>
</template>

<style scoped>
.forgot-password-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #ff6741 0%, #ff8c5c 100%);
  display: flex;
  flex-direction: column;
}

.forgot-password-header {
  background: transparent;
}

.forgot-password-header :deep(.van-nav-bar) {
  background: transparent;
  color: #fff;
}

.forgot-password-header :deep(.van-nav-bar__title) {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
}

.forgot-password-header :deep(.van-nav-bar__left .van-icon) {
  color: #fff;
  font-size: 20px;
}

.forgot-password-form {
  flex: 1;
  padding: 20px 20px 0;
}

.form-card {
  background: #fff;
  border-radius: 20px;
  padding: 40px 30px;
  box-shadow: 0 -8px 30px rgba(0, 0, 0, 0.1);
}

.form-title {
  text-align: center;
  margin-bottom: 40px;
}

.form-title h2 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 10px 0;
}

.form-title p {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.form-fields {
  margin-bottom: 40px;
}

.form-fields .van-field {
  background: #f5f5f5;
  border-radius: 12px;
  margin-bottom: 20px;
  padding: 0 20px;
  height: 56px;
  display: flex;
  align-items: center;
}

.code-field {
  position: relative;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 12px;
  margin-bottom: 20px;
  height: 56px;
}

.code-field .van-field {
  flex: 1;
  background: transparent;
  margin-bottom: 0;
  height: 100%;
}

.code-btn {
  margin-right: 15px;
  height: 36px !important;
  padding: 0 16px !important;
  font-size: 14px;
  border-radius: 18px;
  background: linear-gradient(90deg, #ff6741, #ff8c5c) !important;
  border: none !important;
  color: #fff !important;
  flex-shrink: 0;
}

.form-actions {
  margin-bottom: 30px;
}

.reset-btn {
  height: 50px !important;
  font-size: 18px;
  font-weight: 600;
}

.form-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.login-link {
  font-size: 15px;
  color: #ff6741;
  font-weight: 500;
  cursor: pointer;
}

/* 自定义字段样式 */
:deep(.van-field__left-icon) {
  color: #969799;
  margin-right: 12px;
  font-size: 18px;
}

:deep(.van-field__control) {
  font-size: 16px;
  color: #333;
  line-height: 1.5;
}

:deep(.van-field__control)::placeholder {
  color: #969799;
  font-size: 16px;
  line-height: 1.5;
}

:deep(.van-field) {
  border: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  min-height: 56px;
}

:deep(.van-field__body) {
  display: flex;
  align-items: center;
  width: 100%;
}

:deep(.van-field--focused) {
  background: #fff;
  box-shadow: 0 0 0 2px rgba(255, 103, 65, 0.2);
}

:deep(.van-field__left-icon) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.van-field__control) {
  display: flex;
  align-items: center;
}

/* 自定义按钮样式 */
:deep(.van-button--primary) {
  background: linear-gradient(90deg, #ff6741, #ff8c5c);
  border: none;
  box-shadow: 0 4px 15px rgba(255, 103, 65, 0.3);
}

:deep(.van-button--primary:active) {
  background: linear-gradient(90deg, #e55a37, #e5804f);
}
</style>
