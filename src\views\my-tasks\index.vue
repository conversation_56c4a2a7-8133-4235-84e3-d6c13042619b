<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from '@/utils/toast'
import { useTaskStore } from '@/stores/tasks'
import { getMyTasks, type GetMyTasksParams } from '@/api/tasks'

// 路由实例
const router = useRouter()
const route = useRoute()

// 任务状态
const taskStore = useTaskStore()

// 获取初始标签页状态
function getInitialTab(): string {
  const tab = route.query.tab as string
  // 验证标签页是否有效
  const validTabs = ['incomplete', 'rejected', 'completed']
  return validTabs.includes(tab) ? tab : 'incomplete'
}

// 激活标签 - 从URL查询参数获取，默认为'incomplete'
const activeTab = ref(getInitialTab())

// 下拉刷新状态
const refreshing = ref(false)

// 根据标签页获取筛选参数
const getFilterParams = (tab: string): GetMyTasksParams => {
  const baseParams: GetMyTasksParams = {
    page: 1,
    limit: taskStore.myTasksPageSize
  }

  switch (tab) {
    case 'incomplete':
      // 未完成：任务状态为未完成
      return { ...baseParams, rwstatus: 1, spstatus: 0 }
    case 'rejected':
      // 未通过：审批状态为未通过
      return { ...baseParams, rwstatus: 0, spstatus: 1 }
    case 'completed':
      // 已完成：任务状态为已完成
      return { ...baseParams, rwstatus: 2, spstatus: 0 }
    default:
      return { ...baseParams, rwstatus: 0, spstatus: 0 }
  }
}

// 已接任务数据
const myTasks = computed(() => {
  return taskStore.myTaskList
})



// 获取按钮文本和状态
const getButtonInfo = (status: string) => {
  const buttonMap: Record<string, { text: string; disabled: boolean }> = {
    'in_progress': { text: '提交发布链接', disabled: false },
    'accepted': { text: '提交发布链接', disabled: false },
    'completed': { text: '已完成', disabled: true },
    'failed': { text: '重新提交', disabled: false },
    'rejected': { text: '重新提交', disabled: false }
  }
  return buttonMap[status] || { text: '提交发布链接', disabled: false }
}

// 处理任务操作
const handleTaskAction = (task: any) => {
  // 优先使用taskid字段，如果没有则使用id字段
  const taskId = task.taskid
  const submitId = task.id
  if (activeTab.value === 'incomplete') {
    // 未完成标签页 - 跳转到任务详情页面，带上提交参数
    router.push(`/task-detail/${taskId}?action=submit&submitId=${submitId}`)
  } else if (activeTab.value === 'rejected') {
    // 未通过标签页 - 跳转到任务详情页面，带上重新提交参数
    router.push(`/task-detail/${taskId}?action=resubmit&submitId=${submitId}`)
  }
}

// 获取我的任务列表（首次加载或刷新）
const fetchMyTasks = async (showLoading = true) => {
  if (showLoading) {
    taskStore.setMyTasksLoading(true)
  }

  try {
    // 重置分页状态
    taskStore.resetMyTasksPagination()

    // 获取当前标签页对应的筛选参数
    const filterParams = getFilterParams(activeTab.value)
    taskStore.setMyTasksFilter(filterParams)

    console.log('获取我的任务列表，参数:', filterParams)

    const result = await getMyTasks(filterParams)

    console.log('我的任务列表响应:', {
      tasks: result.tasks.length,
      total: result.total,
      currentPage: result.currentPage,
      hasMore: result.hasMore
    })

    // 设置任务列表和分页信息
    taskStore.setMyTaskList(result.tasks)
    taskStore.setMyTasksPaginationInfo({
      total: result.total,
      currentPage: result.currentPage,
      hasMore: result.hasMore
    })

  } catch (error: any) {
    console.error('获取我的任务列表失败:', error)
    const errorMessage = error.message || '获取任务列表失败'
    showToast(errorMessage)
  } finally {
    if (showLoading) {
      taskStore.setMyTasksLoading(false)
    }
    taskStore.setMyTasksLoadingMore(false)
  }
}

// 加载更多我的任务
const loadMoreMyTasks = async () => {
  if (taskStore.isLoadingMoreMyTasks || !taskStore.myTasksHasMore) {
    return
  }

  taskStore.setMyTasksLoadingMore(true)

  try {
    const filterParams = {
      ...taskStore.myTasksFilter,
      page: taskStore.myTasksCurrentPage + 1,
      limit: taskStore.myTasksPageSize
    }

    console.log('加载更多我的任务，参数:', filterParams)

    const result = await getMyTasks(filterParams)

    console.log('加载更多我的任务响应:', {
      newTasks: result.tasks.length,
      total: result.total,
      currentPage: result.currentPage,
      hasMore: result.hasMore
    })

    // 追加任务列表并更新分页信息
    taskStore.appendMyTaskList(result.tasks)
    taskStore.setMyTasksPaginationInfo({
      total: result.total,
      currentPage: result.currentPage,
      hasMore: result.hasMore
    })

  } catch (error: any) {
    console.error('加载更多我的任务失败:', error)
    const errorMessage = error.message || '加载更多失败'
    showToast(errorMessage)
  } finally {
    taskStore.setMyTasksLoadingMore(false)
  }
}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  try {
    await fetchMyTasks(false)
  } finally {
    refreshing.value = false
  }
}

// 上拉加载更多
const onLoad = async () => {
  await loadMoreMyTasks()
}

// 更新URL查询参数
const updateUrlQuery = (tab: string) => {
  const currentQuery = { ...route.query }

  if (tab === 'incomplete') {
    // 如果是默认标签页，移除tab参数
    delete currentQuery.tab
  } else {
    // 否则设置tab参数
    currentQuery.tab = tab
  }

  router.replace({
    path: route.path,
    query: currentQuery
  })
}

// 监听标签页切换
watch(activeTab, async (newTab) => {
  console.log('标签页切换到:', newTab)

  // 只有当前路由是my-tasks页面时才处理标签页切换
  if (route.path !== '/my-tasks') {
    return
  }

  // 更新URL查询参数
  updateUrlQuery(newTab)
  // 获取新标签页的数据
  await fetchMyTasks()
})

// 监听路由查询参数变化（处理浏览器前进/后退）
watch(() => route.query.tab, () => {
  // 只有当前路由是my-tasks页面时才处理查询参数变化
  if (route.path !== '/my-tasks') {
    return
  }

  const validTab = getInitialTab()
  if (activeTab.value !== validTab) {
    activeTab.value = validTab
    // 注意：这里不需要调用fetchMyTasks，因为activeTab的watch会处理
  }
})

onMounted(async () => {
  // 页面加载时获取任务数据
  console.log('我的任务页面加载，开始获取数据')
  await fetchMyTasks()
})
</script>

<template>
  <div class="my-tasks-container">
    <!-- 导航栏 -->
    <van-nav-bar title="任务大厅" fixed placeholder>
      <template #right>
        <van-icon name="replay" size="18" @click="fetchMyTasks" />
      </template>
    </van-nav-bar>

    <!-- 标签页 -->
    <van-tabs v-model:active="activeTab" sticky color="#ff6b35" line-width="30px">
      <van-tab name="incomplete" title="未完成"></van-tab>
      <van-tab name="rejected" title="未通过"></van-tab>
      <van-tab name="completed" title="已完成"></van-tab>
    </van-tabs>
    
    <!-- 任务列表 -->
    <div class="tasks-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          :loading="taskStore.isLoadingMoreMyTasks"
          :finished="!taskStore.myTasksHasMore"
          finished-text="没有更多了"
          loading-text="加载中..."
          :offset="50"
          @load="onLoad"
        >
          <div
            v-for="task in myTasks"
            :key="task.id"
            class="task-card"
          >
            <div class="task-content">
              <div class="task-info">
                <h3 class="task-title">{{ task.title }}</h3>
                <p class="task-desc">{{ task.description }}</p>
              </div>
              <div class="task-action" v-if="activeTab !== 'completed'">
                <van-button
                  :disabled="getButtonInfo(task.status).disabled"
                  size="small"
                  round
                  color="#ff6b35"
                  @click.stop="handleTaskAction(task)"
                >
                  {{ getButtonInfo(task.status).text }}
                </van-button>
              </div>
            </div>

            <div class="task-footer">
              <span class="task-hint">不提交则任务结束自动采集链接</span>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="myTasks.length === 0 && !taskStore.isLoadingMyTasks" class="empty-state">
            <van-empty
              description="暂无已接任务"
              image="search"
            >
              <van-button
                type="primary"
                round
                @click="router.push('/tasks')"
                color="#ff6b35"
              >
                去接任务
              </van-button>
            </van-empty>
          </div>

          <!-- 加载状态 -->
          <div v-if="taskStore.isLoadingMyTasks && myTasks.length === 0" class="loading-state">
            <van-loading size="24px" vertical>加载中...</van-loading>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    
    <!-- 底部导航 -->
    <van-tabbar route fixed>
      <van-tabbar-item icon="apps-o" to="/tasks">任务</van-tabbar-item>
      <van-tabbar-item icon="completed" to="/my-tasks">已接任务</van-tabbar-item>
      <van-tabbar-item icon="user-o" to="/user">个人中心</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<style scoped>
.my-tasks-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;
}



.tasks-list {
  padding: 8px 16px 80px;
}

.task-card {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.task-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  cursor: pointer;
}

.task-info {
  flex: 1;
  margin-right: 12px;
}

.task-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.task-desc {
  font-size: 14px;
  color: #969799;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.task-action {
  flex-shrink: 0;
}

.task-footer {
  padding: 12px 16px;
  background-color: #f7f8fa;
  border-top: 1px solid #ebedf0;
}

.task-hint {
  font-size: 12px;
  color: #969799;
  line-height: 1.4;
}



.empty-state {
  padding: 60px 0;
  text-align: center;
}

.loading-state {
  padding: 60px 0;
  text-align: center;
}

.van-pull-refresh {
  min-height: calc(100vh - 250px);
}

/* 自定义标签页样式 */
:deep(.van-tabs__nav) {
  background-color: #fff;
}

:deep(.van-tabs__line) {
  background-color: #ff6b35;
}

:deep(.van-tab--active) {
  color: #ff6b35;
  font-weight: 600;
}
</style> 