<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { TabbarItem, Tabbar } from 'vant';

const router = useRouter();
const route = useRoute();

// 当前激活的标签页
const active = ref(0); // 使用ref而不是computed，让v-model可以正确修改它

// 在组件挂载时设置初始激活状态
const updateActive = () => {
  const path = route.path;
  if (path.startsWith('/tasks')) active.value = 0;
  else if (path.startsWith('/my-tasks')) active.value = 1;
  else if (path.startsWith('/user')) active.value = 2;
  else active.value = 0;
};

// 监听路由变化
router.afterEach(() => {
  updateActive();
});

// 初始化
updateActive();

// 切换标签页
const onChange = (index: number) => {
  switch (index) {
    case 0:
      router.push('/tasks');
      break;
    case 1:
      router.push('/my-tasks');
      break;
    case 2:
      router.push('/user');
      break;
  }
};
</script>

<template>
  <div class="tab-bar">
    <van-tabbar 
      v-model="active" 
      @change="onChange"
      active-color="#FF6633"
      inactive-color="#646566"
    >
      <van-tabbar-item icon="home-o">任务大厅</van-tabbar-item>
      <van-tabbar-item icon="orders-o">已接任务</van-tabbar-item>
      <van-tabbar-item icon="user-o">个人中心</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<style scoped>
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
}

/* 自定义激活状态样式 */
:deep(.van-tabbar-item--active) {
  font-weight: bold;
}

/* 可能需要根据小红书风格进一步定制颜色 */
:deep(.van-tabbar-item__icon) {
  font-size: 22px;
}
</style> 