/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TabBar: typeof import('./src/components/TabBar.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanCascader: typeof import('vant/es')['Cascader']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanIcon: typeof import('vant/es')['Icon']
    VanImage: typeof import('vant/es')['Image']
    VanList: typeof import('vant/es')['List']
    VanLoading: typeof import('vant/es')['Loading']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanNoticeBar: typeof import('vant/es')['NoticeBar']
    VanPicker: typeof import('vant/es')['Picker']
    VanPopup: typeof import('vant/es')['Popup']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanRadio: typeof import('vant/es')['Radio']
    VanRadioGroup: typeof import('vant/es')['RadioGroup']
    VanTab: typeof import('vant/es')['Tab']
    VanTabbar: typeof import('vant/es')['Tabbar']
    VanTabbarItem: typeof import('vant/es')['TabbarItem']
    VanTabs: typeof import('vant/es')['Tabs']
    VanUploader: typeof import('vant/es')['Uploader']
  }
}
