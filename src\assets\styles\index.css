/* 全局样式 */
html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
  height: 100%;
  font-size: 16px;
  background-color: #f7f8fa;
  color: #323233;
  box-sizing: border-box;
}

*, *:before, *:after {
  box-sizing: inherit;
}

#app {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

/* 修改Vant主题颜色 */
:root:root {
  --van-primary-color: #FF6633;
}

/* 通用边距 */
.page-container {
  padding: 16px;
}

/* 通用卡片样式 */
.card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 修复移动端点击延迟 */
a, button, input, textarea {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* 自适应图片 */
img {
  max-width: 100%;
  height: auto;
}

/* 防止页面溢出 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
} 