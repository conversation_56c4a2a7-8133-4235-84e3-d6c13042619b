# 小红书任务H5

## 项目概述
这是一个基于Vue 3的小红书任务H5项目，采用现代化的UI设计，提供完整的任务管理功能。用户可以浏览任务、查看详情、完成任务、领取奖励，并管理个人信息。

## 功能特性
- ✨ **用户认证系统** - 登录与注册功能，支持手机号验证
- 📋 **任务管理** - 任务列表展示，支持搜索和状态筛选
- 🎯 **任务详情** - 详细的任务说明、步骤指引和进度跟踪
- 💰 **奖励系统** - 任务完成后可领取奖励
- 📊 **数据统计** - 个人任务完成情况和收益统计
- 👤 **个人中心** - 用户信息管理和功能菜单
- 📱 **响应式设计** - 适配各种移动设备屏幕

## 技术栈
- **前端框架**: Vue 3 (Composition API + setup语法糖)
- **开发语言**: TypeScript
- **构建工具**: Vite
- **路由管理**: Vue Router 4
- **状态管理**: Pinia
- **网络请求**: Axios
- **UI组件库**: Vant 4
- **样式处理**: Sass

## 项目结构
```
├── public/                 # 静态资源
├── src/
│   ├── api/                # API接口
│   │   ├── index.ts        # axios配置
│   │   ├── tasks.ts        # 任务相关API
│   │   └── user.ts         # 用户相关API
│   ├── assets/             # 项目资源文件
│   ├── components/         # 通用组件
│   ├── router/             # 路由配置
│   │   └── index.ts        # 路由定义和守卫
│   ├── stores/             # Pinia状态管理
│   │   ├── tasks.ts        # 任务状态管理
│   │   └── user.ts         # 用户状态管理
│   ├── views/              # 页面视图组件
│   │   ├── login/          # 登录页面
│   │   ├── register/       # 注册页面
│   │   ├── tasks/          # 任务列表页面
│   │   ├── task-detail/    # 任务详情页面
│   │   ├── my-tasks/       # 已接任务页面
│   │   └── user/           # 个人中心页面
│   ├── App.vue             # 根组件
│   └── main.ts             # 入口文件
├── .gitignore              # Git忽略配置
├── index.html              # HTML模板
├── package.json            # 项目依赖配置
├── tsconfig.json           # TypeScript配置
├── vite.config.ts          # Vite配置
└── README.md               # 项目文档
```

## 安装与运行
```bash
# 安装依赖
npm install

# 本地开发
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 页面功能详述

### 1. 登录页面 (`/login`)
- 🎨 现代化渐变背景设计
- 📱 手机号和密码登录
- ✅ 表单验证和错误提示
- 🚀 快速登录功能（开发测试用）
- 🔗 注册页面跳转

### 2. 注册页面 (`/register`)
- 📝 用户注册功能
- 🔒 密码强度验证
- 📧 邮箱/手机号验证

### 3. 任务列表页面 (`/tasks`)
- 🔍 任务搜索功能
- 🏷️ 状态标签筛选（全部/待完成/已完成）
- 📊 任务进度条显示
- 💰 奖励金额展示
- ⏰ 任务截止时间
- 📱 下拉刷新和上拉加载

### 4. 任务详情页面 (`/task-detail/:id`)
- 📄 详细任务描述
- 📈 任务进度可视化
- 💡 步骤式任务指引
- ⚠️ 注意事项说明
- 🎯 一键完成任务
- 🏆 奖励领取功能

### 5. 已接任务页面 (`/my-tasks`)
- 📊 任务统计卡片
- 🏷️ 状态分类展示
- 💰 总收益统计
- ✅ 已完成任务管理

### 6. 个人中心页面 (`/user`)
- 👤 用户信息展示
- 💳 余额显示
- 📊 任务完成统计
- ⚙️ 功能菜单
- 🚪 退出登录

## 设计特色

### UI设计
- 🎨 **橙色主题**: 使用`#ff6b35`作为主色调，营造活跃的氛围
- 🌈 **渐变效果**: 多处使用渐变背景，提升视觉层次
- 🎯 **圆角设计**: 统一的12px圆角，现代化视觉体验
- 💫 **微动效**: hover动画和过渡效果，增强交互体验
- 📱 **移动优先**: 专为移动设备优化的界面设计

### 交互体验
- ⚡ **快速响应**: 统一的loading状态和反馈提示
- 🔄 **状态管理**: 实时同步的数据状态
- 📱 **手势操作**: 支持下拉刷新和触摸操作
- 🎯 **直观导航**: 底部导航栏快速切换

## 数据模型

### 任务数据结构
```typescript
interface Task {
  id: string            // 任务ID
  title: string         // 任务标题
  description: string   // 任务描述
  reward: number        // 奖励金额
  status: 'pending' | 'completed' | 'failed'  // 任务状态
  steps: string[]       // 任务步骤
  progress?: number     // 完成进度 (0-100)
  deadline?: string     // 截止时间
}
```

### 用户数据结构
```typescript
interface UserInfo {
  userId?: string       // 用户ID
  id?: string          // 备用ID
  username?: string    // 用户名
  nickname?: string    // 昵称
  avatar?: string      // 头像URL
  phone?: string       // 手机号
  level?: string       // 用户等级
  balance?: number     // 账户余额
}
```

## 开发规范

### 代码规范
- 使用TypeScript进行类型检查
- 遵循Vue 3 Composition API最佳实践
- 使用ESLint和Prettier保证代码质量
- 组件命名采用PascalCase
- 变量命名采用camelCase

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 样式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建工具或辅助工具的变动

## 浏览器支持
- Chrome ≥ 60
- Firefox ≥ 60
- Safari ≥ 12
- iOS Safari ≥ 12
- Android Browser ≥ 60

## 部署说明
1. 运行 `npm run build` 构建项目
2. 将 `dist` 目录上传到服务器
3. 配置nginx或其他web服务器
4. 确保单页应用路由正确配置

## 后续计划
- [ ] 添加实时通知功能
- [ ] 集成第三方登录（微信、QQ等）
- [ ] 增加任务分享功能
- [ ] 优化性能和加载速度
- [ ] 增加暗黑模式支持
- [ ] 添加多语言支持

## 许可证
MIT License 