<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showDialog, showLoadingToast, closeToast } from 'vant'
import { showToast } from '@/utils/toast'
import { useUserStore } from '@/stores/user'
import { useTaskStore } from '@/stores/tasks'
import { logout } from '@/api/user'

// 路由实例
const router = useRouter()

// 用户状态
const userStore = useUserStore()
const taskStore = useTaskStore()

// 加载状态
const isLoadingUserInfo = ref(false)

// 统计数据
const stats = computed(() => {
  const tasks = taskStore.taskList
  return {
    totalTasks: tasks.length,
    completedTasks: tasks.filter(task => task.status === 'completed').length,
    pendingTasks: tasks.filter(task => task.status === 'pending').length,
    totalReward: tasks
      .filter(task => task.status === 'completed')
      .reduce((sum, task) => sum + (task.reward || 0), 0)
  }
})

// 获取用户信息 - 直接使用store中的数据
const userInfo = computed(() => {
  return userStore.userInfo
})

// 获取用户详细信息（包含更多字段）
const memberInfo = computed(() => {
  return userStore.memberInfo
})

// 格式化积分显示
const formatPoints = (points: number | undefined) => {
  if (points === undefined || points === null) return '0'
  // 如果积分大于10000，显示为 1.2万 的格式
  if (points >= 10000) {
    return (points / 10000).toFixed(1) + '万'
  }
  // 添加千分位分隔符
  return points.toLocaleString()
}

// 菜单项配置
const menuItems = computed(() => [
  {
    title: '可用积分',
    icon: 'gold-coin-o',
    value: formatPoints(memberInfo.value?.jifen),
    path: '/points',
    showValue: true,
    loading: isLoadingUserInfo.value && !memberInfo.value
  },
  {
    title: '排行榜',
    icon: 'medal-o',
    path: '/ranking'
  },
  {
    title: '账号库',
    icon: 'contact',
    path: '/accounts'
  },
  {
    title: memberInfo.value?.openid ? '微信已绑定' : '绑定微信',
    icon: 'wechat',
    action: 'wechat',
    showStatus: true,
    status: memberInfo.value?.openid ? 'bound' : 'unbound',
    disabled: !!memberInfo.value?.openid // 已绑定时禁用点击
  }
])

// 获取用户信息
const loadUserInfo = async () => {
  if (isLoadingUserInfo.value) return
  
  isLoadingUserInfo.value = true
  
  try {
    const memberData = await userStore.fetchMemberInfo()
    if (memberData) {
      console.log('用户信息加载成功:', memberData)
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  } finally {
    isLoadingUserInfo.value = false
  }
}

// 跳转到任务列表
const goToTasks = () => {
  router.push('/tasks')
}

// 跳转到已接任务
const goToMyTasks = () => {
  router.push('/my-tasks')
}

// 处理微信绑定
const handleWechatAction = async () => {
  const isWechatBound = !!memberInfo.value?.openid

  if (isWechatBound) {
    // 已绑定，不执行任何操作
    showToast('微信已绑定')
    return
  }

  // 未绑定，跳转到绑定页面
  router.push('/bind-wechat')
}

// 点击菜单项
const handleMenuClick = (item: any) => {
  // 检查是否禁用
  if (item.disabled) {
    return
  }

  if (item.action === 'wechat') {
    // 处理微信绑定
    handleWechatAction()
  } else if (item.path) {
    if (item.path === '/accounts') {
      // 跳转到账号库页面
      router.push(item.path)
    } else {
      // 其他功能暂时显示提示
      showToast(`跳转到 ${item.title}`)
    }
  }
}

// 退出登录
const handleLogout = async () => {
  try {
    await showDialog({
      title: '退出登录',
      message: '确定要退出登录吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      showCancelButton: true,
    })

    // 显示加载提示
    const loadingToast = showLoadingToast({
      message: '退出中...',
      forbidClick: true,
    })

    try {
      // 调用退出登录API
      const response = await logout()
      
      // 关闭加载提示
      closeToast()
      
      if (response.code === 1) {
        // 清除用户信息
        userStore.clearUserInfo()
        // 清除token
        localStorage.removeItem('token')
        // 提示成功
        showToast(response.msg || '退出成功')
        // 跳转到登录页
        router.replace('/login')
      } else {
        // API返回失败信息
        showToast(response.msg || '退出失败')
      }
    } catch (error) {
      // 关闭加载提示
      closeToast()
      console.error('退出登录失败:', error)
      // 即使API调用失败，也可以执行本地退出
      userStore.clearUserInfo()
      localStorage.removeItem('token')
      showToast('退出成功')
      router.replace('/login')
    }
  } catch (error) {
    // 用户取消了对话框，不需要处理
  }
}

// 页面加载时的操作
onMounted(() => {
  // 加载用户信息
  loadUserInfo()
})
</script>

<template>
  <div class="user-container">
    <!-- 导航栏 -->
    <van-nav-bar 
      title="个人中心" 
      fixed 
      placeholder
      background="#ff6b35"
      color="#fff"
    >
      <template #right>
        <van-icon 
          name="replay" 
          size="18" 
          color="#fff" 
          @click="loadUserInfo"
          :class="{ 'rotate': isLoadingUserInfo }"
        />
      </template>
    </van-nav-bar>
    
    <!-- 用户信息卡片 -->
    <div class="user-header">
      <div class="user-bg">
        <!-- 用户信息加载中 -->
        <div v-if="isLoadingUserInfo && !userInfo" class="user-loading">
          <van-loading size="24" color="#fff" />
          <p class="loading-text">加载用户信息中...</p>
        </div>
        
        <!-- 用户信息展示 -->
        <div v-else class="user-profile">
          <div class="user-avatar">
            <van-image
              :src="userInfo?.avatar || '/assets/img/avatar.png'"
              round
              width="60"
              height="60"
              fit="cover"
              error="默认头像"
            />
          </div>
          <div class="user-info">
            <h3 class="username">{{ userInfo?.nickname || memberInfo?.mobile || userInfo?.phone }}</h3>
            <!-- <p class="user-store">门店/部门：总部</p> -->
            <!-- 加载状态指示 -->
            <van-loading v-if="isLoadingUserInfo" size="14" color="#fff" />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 功能菜单 -->
    <div class="menu-section">
      <div class="menu-list">
        <div
          v-for="item in menuItems"
          :key="item.title"
          :class="['menu-item', { 'menu-item-disabled': item.disabled }]"
          @click="handleMenuClick(item)"
        >
          <div class="menu-icon">
            <van-icon :name="item.icon" size="20" />
          </div>
          <div class="menu-content">
            <div class="menu-title">{{ item.title }}</div>
          </div>
          <div v-if="item.showValue" class="menu-value">
            <van-loading v-if="item.loading" size="14" color="#ff6b35" />
            <span v-else>{{ item.value }}</span>
          </div>
          <div v-if="item.showStatus" class="menu-status">
            <van-tag
              v-if="item.status === 'bound'"
              type="success"
            >
              已绑定
            </van-tag>
            <van-tag
              v-else
              type="default"
            >
              未绑定
            </van-tag>
          </div>
          <van-icon name="arrow" class="menu-arrow" />
        </div>
      </div>
    </div>
    
    <!-- 退出账号 -->
    <div class="logout-section">
      <van-button
        type="danger"
        block
        round
        @click="handleLogout"
        color="#ff4444"
      >
        退出账号
      </van-button>
    </div>
    
    <!-- 底部导航 -->
    <van-tabbar route fixed>
      <van-tabbar-item icon="apps-o" to="/tasks">任务</van-tabbar-item>
      <van-tabbar-item icon="completed" to="/my-tasks">已接任务</van-tabbar-item>
      <van-tabbar-item icon="user-o" to="/user">个人中心</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<style scoped>
.user-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;
}

.user-header {
  margin-bottom: 16px;
}

.user-bg {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8c69 100%);
  padding: 20px 16px 30px;
  position: relative;
}

.user-profile {
  display: flex;
  align-items: center;
  color: #fff;
}

.user-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #fff;
  min-height: 80px;
}

.loading-text {
  margin: 12px 0 0 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.user-avatar {
  position: relative;
  margin-right: 16px;
}

.user-info {
  flex: 1;
  margin-left: 16px;
}

.username {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #fff;
}

.user-store {
  font-size: 13px;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

.stats-section {
  margin: 0 16px 16px;
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stats-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #969799;
}

.stat-icon {
  position: absolute;
  top: 12px;
  right: 12px;
  font-size: 20px;
  color: #969799;
}

.stat-icon.completed {
  color: #07c160;
}

.stat-icon.pending {
  color: #ff976a;
}

.stat-icon.reward {
  color: #ffd700;
}

.menu-section {
  margin: 0 16px 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.menu-list {
  padding: 8px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.menu-item:hover {
  background-color: #f8f9fa;
}

.menu-item:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}

.menu-item-disabled {
  opacity: 0.6;
  cursor: not-allowed !important;
}

.menu-item-disabled:hover {
  background-color: transparent !important;
}

.menu-icon {
  width: 40px;
  height: 40px;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #ff6b35;
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 15px;
  font-weight: 500;
  color: #323233;
}

.menu-value {
  font-size: 14px;
  color: #ff6b35;
  font-weight: 600;
  margin-right: 8px;
}

.menu-status {
  margin-right: 8px;
}

.menu-arrow {
  color: #c8c9cc;
  font-size: 14px;
}

.logout-section {
  margin: 0 16px;
}

/* 自定义导航栏样式 */
:deep(.van-nav-bar) {
  background: linear-gradient(90deg, #ff6b35, #ff8c69);
}

:deep(.van-nav-bar__title) {
  color: #fff;
  font-weight: 600;
}

/* 自定义按钮样式 */
:deep(.van-button--primary) {
  background: #ff6b35;
  border-color: #ff6b35;
}

/* 刷新按钮旋转动画 */
.rotate {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 