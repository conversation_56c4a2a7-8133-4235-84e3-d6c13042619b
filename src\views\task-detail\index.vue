<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showDialog, showLoadingToast, closeToast } from 'vant'
import { showToast } from '@/utils/toast'
import { getTaskDetail, completeTask, claimReward, claimTask } from '@/api/tasks'
import { useTaskStore } from '@/stores/tasks'

// 路由信息
const route = useRoute()
const router = useRouter()

// 任务ID - 使用计算属性确保响应路由变化
const taskId = computed(() => route.params.id as string)

// 提交ID - 使用计算属性确保响应路由变化
const submitId = computed(() => route.query.submitId as string)

// 检查是否来自提交按钮的跳转
const actionType = computed(() => route.query.action as string)

// 任务状态
const taskStore = useTaskStore()

// 任务详情
const taskDetail = ref<any>(null)

// 加载状态
const loading = ref(false)
const claimTaskLoading = ref(false)
const claimLoading = ref(false)

// 计算状态标签类型
const statusType = computed(() => {
  if (!taskDetail.value) return 'default'
  const typeMap: Record<string, 'default' | 'primary' | 'success' | 'warning' | 'danger'> = {
    'pending': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return typeMap[taskDetail.value.status] || 'default'
})

// 计算状态文本
const statusText = computed(() => {
  if (!taskDetail.value) return ''
  const textMap: Record<string, string> = {
    'pending': '待完成',
    'completed': '已完成',
    'failed': '已失败'
  }
  return textMap[taskDetail.value.status] || taskDetail.value.status
})

// 获取任务详情
const fetchTaskDetail = async () => {
  loading.value = true
  const toast = showLoadingToast({
    message: '加载中...',
    forbidClick: true,
  })
  
  try {
    // 先从store中获取数据作为临时显示（提升用户体验）
    const storeTask = taskStore.getTaskById(taskId.value)
    if (storeTask) {
      taskDetail.value = storeTask
    }

    // 始终调用详情API获取最新的完整数据
    console.log('调用任务详情API，任务ID:', taskId.value)
    const res = await getTaskDetail(taskId.value)
    taskDetail.value = res
    console.log('任务详情获取成功:', res)
    
  } catch (error: any) {
    console.error('获取任务详情失败:', error)
    // 检查是否是未登录错误
    if (error && error.message === "未登录") {
      // 未登录错误已经在拦截器中处理了跳转，这里不显示toast
      return
    }

    // 如果API调用失败但store中有数据，则使用store数据
    const storeTask = taskStore.getTaskById(taskId.value)
    if (storeTask && !taskDetail.value) {
      taskDetail.value = storeTask
      showToast('使用缓存数据，可能不是最新信息')
    } else {
      // 业务代码控制错误提示
      const errorMessage = error.message || '获取任务详情失败，请稍后重试'
      showToast(errorMessage)
    }
  } finally {
    loading.value = false
    closeToast()
  }
}

// 领取任务
const handleClaimTask = async () => {
  showDialog({
    title: '确认领取任务',
    message: '您确定要领取这个任务吗？领取后请在规定时间内完成任务。',
    confirmButtonText: '确认领取',
    cancelButtonText: '再想想',
    showCancelButton: true,
  }).then(async () => {
    claimTaskLoading.value = true
    
    try {
      const result = await claimTask(taskId.value)

      // 根据响应code判断结果
      if (result.code === 1) {
        // 领取成功
        // showToast('任务领取成功！')
        // 更新任务状态为已领取
        if (taskDetail.value) {
          taskDetail.value.status = 'claimed'
          taskStore.setTaskStatus(taskId.value, 'claimed')
        }
        
        // 显示选择弹窗
        showDialog({
          title: '任务领取成功！',
          message: '恭喜您成功领取任务！请选择您接下来的操作：',
          confirmButtonText: '查看已接任务',
          cancelButtonText: '返回任务大厅',
          showCancelButton: true,
          confirmButtonColor: '#ff6b35',
          cancelButtonColor: '#969799'
        }).then(() => {
          // 点击"查看已接任务" - 跳转到我的任务页面
          router.push('/my-tasks')
        }).catch(() => {
          // 点击"返回任务大厅" - 跳转到任务列表页面
          router.push('/tasks')
        })
      } else {
        // 这个分支实际上不会执行，因为拦截器会处理 code !== 1 的情况
        // 保留作为备用
        showToast(result.msg || '领取任务失败')
      }
    } catch (error: any) {
      console.error('领取任务失败:', error)

      // 检查是否是未登录错误
      if (error && error.message === "未登录") {
        // 未登录错误已在拦截器中处理，不显示toast
        return
      }

      // 业务代码完全控制错误提示的显示
      const errorMessage = error.message || '领取任务失败，请稍后重试'

      // 只显示一次错误提示
      showToast(errorMessage)
    } finally {
      claimTaskLoading.value = false
    }
  }).catch(() => {
    // 取消操作
  })
}

// 领取奖励
const handleClaimReward = async () => {
  claimLoading.value = true

  try {
    await claimReward(taskId.value)
    showToast('奖励领取成功！')
    // 可以跳转到奖励页面或更新用户余额
  } catch (error) {
    console.error('领取奖励失败:', error)
    showToast('领取奖励失败')
  } finally {
    claimLoading.value = false
  }
}

// 复制标题到剪贴板
const handleCopyTitle = async () => {
  if (!taskDetail.value?.title) {
    showToast('没有可复制的标题')
    return
  }

  try {
    await navigator.clipboard.writeText(taskDetail.value.title)
    showToast('标题已复制到剪贴板')
  } catch (error) {
    console.error('复制标题失败:', error)
    // 降级方案：使用传统的复制方法
    try {
      const textArea = document.createElement('textarea')
      textArea.value = taskDetail.value.title
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      showToast('标题已复制到剪贴板')
    } catch (fallbackError) {
      console.error('降级复制方法也失败:', fallbackError)
      showToast('复制失败，请手动复制')
    }
  }
}

// 复制内容到剪贴板
const handleCopyContent = async () => {
  if (!taskDetail.value?.description) {
    showToast('没有可复制的内容')
    return
  }

  try {
    // 移除HTML标签，只复制纯文本
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = taskDetail.value.description
    const plainText = tempDiv.textContent || tempDiv.innerText || ''

    await navigator.clipboard.writeText(plainText)
    showToast('内容已复制到剪贴板')
  } catch (error) {
    console.error('复制内容失败:', error)
    // 降级方案：使用传统的复制方法
    try {
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = taskDetail.value.description
      const plainText = tempDiv.textContent || tempDiv.innerText || ''

      const textArea = document.createElement('textarea')
      textArea.value = plainText
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      showToast('内容已复制到剪贴板')
    } catch (fallbackError) {
      console.error('降级复制方法也失败:', fallbackError)
      showToast('复制失败，请手动复制')
    }
  }
}

// 提交发布链接
const handleSubmitLink = () => {
  // 跳转到提交链接页面
  // taskId.value 是从路由参数获取的，应该是来自任务列表的taskid（用于获取详情）
  // 我们需要传递submitId参数，这个应该是任务列表中的id字段（用于提交）
  // 由于当前只有taskId，我们暂时使用相同的值，后续需要从任务列表传递正确的submitId
  router.push({
    path: `/submit-task/${taskId.value}`,
    query: {
      action: actionType.value,
      submitId: submitId.value // 这里需要传递任务列表中的id字段
    }
  })
}

// 返回任务列表
const goBack = () => {
  router.back()
}

// 监听路由参数变化，当切换任务时重新获取详情
watch(() => route.params.id, (newId, oldId) => {
  if (newId && newId !== oldId) {
    console.log('任务ID变化，重新获取详情:', { oldId, newId })
    // 清空当前任务详情，避免显示旧数据
    taskDetail.value = null
    // 重新获取任务详情
    fetchTaskDetail()
  }
}, { immediate: false })

// 页面加载时获取任务详情
onMounted(() => {
  fetchTaskDetail()
})
</script>

<template>
  <div class="task-detail-container">
    <!-- 导航栏 -->
    <van-nav-bar
      title="任务详情"
      left-arrow
      left-text="返回"
      @click-left="goBack"
      fixed
      placeholder
      background="#ff6b35"
      color="#fff"
    />
    
    <!-- 任务内容 -->
    <div class="task-content" v-if="taskDetail">
      <!-- 任务卡片 -->
      <div class="task-card">
        <div class="task-header">
          <h1 class="task-title">{{ taskDetail.title }}</h1>
          <div class="task-description" v-html="taskDetail.description"></div>
        </div>
        
        
        <!-- 任务信息 -->
        <div class="task-info-grid">
          <div class="info-item">
            <div class="info-icon">
              <van-icon name="gold-coin-o" size="24" color="#ff6b35" />
            </div>
            <div class="info-content">
              <div class="info-label">任务积分</div>
              <div class="info-value reward-value">{{ taskDetail.rwtype_score }}</div>
            </div>
          </div>
          
          <div class="info-item">
            <div class="info-icon">
              <van-icon name="clock-o" size="24" color="#969799" />
            </div>
            <div class="info-content">
              <div class="info-label">截止时间</div>
              <div class="info-value">{{ taskDetail.deadline || '长期有效' }}</div>
            </div>
          </div>
          
          <div class="info-item">
            <div class="info-icon">
              <van-icon name="label-o" size="24" color="#07c160" />
            </div>
            <div class="info-content">
              <div class="info-label">难度等级</div>
              <div class="info-value">{{ taskDetail.difficulty || '简单' }}</div>
            </div>
          </div>
          
          <div class="info-item">
            <div class="info-icon">
              <van-icon name="apps-o" size="24" color="#1989fa" />
            </div>
            <div class="info-content">
              <div class="info-label">任务分类</div>
              <div class="info-value">{{ taskDetail.category || '内容创作' }}</div>
            </div>
          </div>
          
          <div class="info-item">
            <div class="info-icon">
              <van-icon name="wechat" size="24" color="#ff6b35" />
            </div>
            <div class="info-content">
              <div class="info-label">平台</div>
              <div class="info-value">{{ taskDetail.mediaPlatform || '小红书' }}</div>
            </div>
          </div>
          
          <div class="info-item">
            <div class="info-icon">
              <van-icon name="friends-o" size="24" color="#969799" />
            </div>
            <div class="info-content">
              <div class="info-label">参与情况</div>
              <div class="info-value">{{ taskDetail.userCount || 0 }}/{{ taskDetail.maxParticipants || 100 }}</div>
            </div>
          </div>
        </div>
        
        <!-- 任务详细信息 -->
        <div class="task-extra-info" v-if="taskDetail.publishTime">
          <div class="extra-info-item" v-if="taskDetail.gj_id">
            <span class="extra-label">任务要求：</span>
            <span class="extra-value">发布到小红书</span>
          </div>
        </div>
      </div>

    </div>
    
    <!-- 空状态 -->
    <div class="empty-state" v-else-if="!loading">
      <van-empty description="任务不存在" image="error" />
    </div>
    
    <!-- 操作按钮 -->
    <div class="task-actions" v-if="taskDetail">
      <!-- 来自提交按钮的跳转，显示三个操作按钮 -->
      <template v-if="actionType === 'submit' || actionType === 'resubmit'">
        <div class="action-buttons-grid">
          <van-button
            type="default"
            round
            size="large"
            @click="handleCopyTitle"
            color="#1989fa"
          >
            复制标题
          </van-button>

          <van-button
            type="primary"
            round
            size="large"
            @click="handleCopyContent"
            color="#1989fa"
          >
            复制内容
          </van-button>

          <van-button
            type="primary"
            round
            size="large"
            @click="handleSubmitLink"
            color="#ff6b35"
          >
            提交发布链接
          </van-button>
        </div>
      </template>

      <!-- 普通任务详情页面 -->
      <template v-else-if="taskDetail.status === 'normal'">
        <van-button
          type="primary"
          block
          round
          size="large"
          @click="handleClaimTask"
          :loading="claimTaskLoading"
          color="#ff6b35"
        >
          领取任务
        </van-button>
      </template>

      <template v-else>
        <van-button
          type="default"
          block
          round
          size="large"
          disabled
        >
          任务已结束
        </van-button>
      </template>
    </div>
  </div>
</template>

<style scoped>
.task-detail-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #ff6b35 0%, #f7f8fa 25%);
  padding-bottom: 80px;
}

.task-content {
  padding: 12px 16px;
}

.task-card {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(255, 107, 53, 0.1);
}

.task-header {
  text-align: center;
  margin-bottom: 20px;
}

.task-status {
  margin-bottom: 12px;
}

.task-title {
  font-size: 20px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.task-description {
  font-size: 14px;
  color: #646566;
  margin: 0;
  line-height: 1.5;
  text-align: left;
}

/* HTML内容样式 */
.task-description :deep(p) {
  margin: 8px 0;
  line-height: 1.5;
}

.task-description :deep(br) {
  line-height: 1.5;
}

.task-description :deep(strong) {
  font-weight: 600;
  color: #323233;
}

.task-description :deep(em) {
  font-style: italic;
  color: #ff6b35;
}

.task-description :deep(ul),
.task-description :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.task-description :deep(li) {
  margin: 4px 0;
  line-height: 1.5;
}

.task-progress-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 14px;
  color: #323233;
  font-weight: 500;
}

.progress-percent {
  font-size: 14px;
  color: #ff6b35;
  font-weight: 600;
}

.task-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 12px;
}

.info-icon {
  margin-right: 12px;
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 12px;
  color: #969799;
  margin-bottom: 2px;
}

.info-value {
  font-size: 14px;
  color: #323233;
  font-weight: 500;
}

.reward-value {
  color: #ff6b35;
  font-weight: 600;
  font-size: 16px;
}

.task-extra-info {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #ff6b35;
  margin-bottom: 20px;
}

.extra-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.extra-info-item:last-child {
  margin-bottom: 0;
}

.extra-label {
  color: #646566;
  margin-right: 8px;
  min-width: 80px;
}

.extra-value {
  color: #323233;
  font-weight: 500;
}

.requirements-section, .steps-section, .notice-section {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.requirements-content {
  padding: 16px;
  background: #fff5f0;
  border-radius: 12px;
  border-left: 4px solid #ff6b35;
}

.requirements-text {
  font-size: 14px;
  color: #323233;
  line-height: 1.6;
  margin: 0;
}

/* 任务说明HTML内容样式 */
.requirements-text :deep(p) {
  margin: 8px 0;
  line-height: 1.6;
}

.requirements-text :deep(br) {
  line-height: 1.6;
}

.requirements-text :deep(strong) {
  font-weight: 600;
  color: #ff6b35;
}

.requirements-text :deep(em) {
  font-style: italic;
  color: #ff6b35;
}

.requirements-text :deep(ul),
.requirements-text :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.requirements-text :deep(li) {
  margin: 4px 0;
  line-height: 1.6;
}

.requirements-text :deep(h1),
.requirements-text :deep(h2),
.requirements-text :deep(h3) {
  color: #323233;
  margin: 12px 0 8px 0;
  font-weight: 600;
}

.requirements-text :deep(h1) {
  font-size: 18px;
}

.requirements-text :deep(h2) {
  font-size: 16px;
}

.requirements-text :deep(h3) {
  font-size: 15px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.section-title span {
  margin-left: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}



.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #f0f0f0;
  color: #969799;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  margin-right: 12px;
  flex-shrink: 0;
  margin-top: 2px;
}

.step-item.completed .step-number {
  background: #ff6b35;
  color: #fff;
}

.step-content {
  flex: 1;
}

.step-text {
  font-size: 14px;
  color: #323233;
  line-height: 1.5;
  margin: 0;
}

.step-item.completed .step-text {
  color: #969799;
  text-decoration: line-through;
}

.notice-content {
  padding: 12px;
  background: #fff5f0;
  border-radius: 8px;
  border-left: 4px solid #ff6b35;
}

.notice-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.notice-list li {
  font-size: 13px;
  color: #646566;
  line-height: 1.5;
  margin-bottom: 4px;
  position: relative;
  padding-left: 12px;
}

.notice-list li:before {
  content: '•';
  color: #ff6b35;
  position: absolute;
  left: 0;
}

.notice-list li:last-child {
  margin-bottom: 0;
}

.task-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: #fff;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
  z-index: 100;
}

.action-buttons-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
}



.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 自定义导航栏样式 */
:deep(.van-nav-bar) {
  background: linear-gradient(90deg, #ff6b35, #ff8c69);
}

:deep(.van-nav-bar__title) {
  color: #fff;
  font-weight: 600;
}

:deep(.van-nav-bar__left) {
  color: #fff;
}

:deep(.van-nav-bar__arrow) {
  color: #fff;
}

:deep(.van-nav-bar__text) {
  color: #fff;
}
</style> 