import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { Task, GetMyTasksParams } from '@/api/tasks'

// 定义任务状态
export const useTaskStore = defineStore('tasks', () => {
  // 任务列表
  const taskList = ref<Task[]>([])
  
  // 分页相关状态
  const total = ref(0) // 任务总数
  const currentPage = ref(1) // 当前页码
  const hasMore = ref(false) // 是否还有更多数据
  const pageSize = ref(10) // 每页数量
  
  // 加载状态
  const isLoading = ref(false)
  const isLoadingMore = ref(false) // 加载更多状态
  
  // 设置任务列表（替换模式）
  function setTaskList(tasks: Task[]) {
    taskList.value = tasks
  }
  
  // 追加任务列表（分页加载更多）
  function appendTaskList(tasks: Task[]) {
    taskList.value.push(...tasks)
  }
  
  // 设置分页信息
  function setPaginationInfo(paginationData: {
    total: number
    currentPage: number
    hasMore: boolean
  }) {
    total.value = paginationData.total
    currentPage.value = paginationData.currentPage
    hasMore.value = paginationData.hasMore
  }
  
  // 重置分页状态
  function resetPagination() {
    console.log('Store: resetPagination 开始')
    taskList.value = []
    total.value = 0
    currentPage.value = 1
    hasMore.value = false
    console.log(`Store: resetPagination 重置 isLoadingMore: ${isLoadingMore.value} → false`)
    isLoadingMore.value = false // 重置加载更多状态
    console.log('Store: resetPagination 完成')
  }
  
  // 获取任务详情
  function getTaskById(id: string) {
    return taskList.value.find(task => task.id === id)
  }
  
  // 设置任务状态
  function setTaskStatus(id: string, status: string) {
    const task = taskList.value.find(task => task.id === id)
    if (task) {
      task.status = status
    }
  }
  
  // 设置加载状态
  function setLoading(loading: boolean) {
    isLoading.value = loading
  }
  
  // 设置加载更多状态
  function setLoadingMore(loading: boolean) {
    console.log(`Store: setLoadingMore(${loading}), 当前值: ${isLoadingMore.value}`)
    isLoadingMore.value = loading
  }

  // 我的任务相关状态
  const myTaskList = ref<Task[]>([])
  const myTasksTotal = ref(0)
  const myTasksCurrentPage = ref(1)
  const myTasksHasMore = ref(false)
  const myTasksPageSize = ref(10)
  const isLoadingMyTasks = ref(false)
  const isLoadingMoreMyTasks = ref(false)

  // 我的任务筛选参数
  const myTasksFilter = ref<GetMyTasksParams>({
    rwstatus: 0,
    spstatus: 0,
    page: 1,
    limit: 10
  })

  // 设置我的任务列表（替换模式）
  function setMyTaskList(tasks: Task[]) {
    myTaskList.value = tasks
  }

  // 追加我的任务列表（分页加载更多）
  function appendMyTaskList(tasks: Task[]) {
    myTaskList.value.push(...tasks)
  }

  // 设置我的任务分页信息
  function setMyTasksPaginationInfo(paginationData: {
    total: number
    currentPage: number
    hasMore: boolean
  }) {
    myTasksTotal.value = paginationData.total
    myTasksCurrentPage.value = paginationData.currentPage
    myTasksHasMore.value = paginationData.hasMore
  }

  // 重置我的任务分页状态
  function resetMyTasksPagination() {
    myTaskList.value = []
    myTasksTotal.value = 0
    myTasksCurrentPage.value = 1
    myTasksHasMore.value = false
    isLoadingMoreMyTasks.value = false
  }

  // 设置我的任务加载状态
  function setMyTasksLoading(loading: boolean) {
    isLoadingMyTasks.value = loading
  }

  // 设置我的任务加载更多状态
  function setMyTasksLoadingMore(loading: boolean) {
    isLoadingMoreMyTasks.value = loading
  }

  // 设置我的任务筛选参数
  function setMyTasksFilter(filter: GetMyTasksParams) {
    myTasksFilter.value = { ...filter }
  }

  return {
    // 状态
    taskList,
    total,
    currentPage,
    hasMore,
    pageSize,
    isLoading,
    isLoadingMore,

    // 我的任务状态
    myTaskList,
    myTasksTotal,
    myTasksCurrentPage,
    myTasksHasMore,
    myTasksPageSize,
    isLoadingMyTasks,
    isLoadingMoreMyTasks,
    myTasksFilter,

    // 方法
    setTaskList,
    appendTaskList,
    setPaginationInfo,
    resetPagination,
    getTaskById,
    setTaskStatus,
    setLoading,
    setLoadingMore,

    // 我的任务方法
    setMyTaskList,
    appendMyTaskList,
    setMyTasksPaginationInfo,
    resetMyTasksPagination,
    setMyTasksLoading,
    setMyTasksLoadingMore,
    setMyTasksFilter
  }
}) 